import tkinter as tk
from tkinter import ttk
from 工具模块 import EMOJI

# 定义现代化的颜色主题
COLORS = {
    'primary': '#2563eb',      # 蓝色主色调
    'primary_light': '#3b82f6',
    'secondary': '#10b981',    # 绿色辅助色
    'accent': '#f59e0b',       # 橙色强调色
    'danger': '#ef4444',       # 红色警告色
    'background': '#f8fafc',   # 浅灰背景
    'surface': '#ffffff',      # 白色表面
    'text_primary': '#1f2937', # 深灰文字
    'text_secondary': '#6b7280', # 中灰文字
    'border': '#e5e7eb'        # 边框颜色
}

# 定义字体样式
FONTS = {
    'title': ('Microsoft YaHei UI', 12, 'bold'),
    'subtitle': ('Microsoft YaHei UI', 10, 'bold'),
    'body': ('Microsoft YaHei UI', 9),
    'small': ('Microsoft YaHei UI', 8),
    'mono': ('Consolas', 9),
    'mono_bold': ('Consolas', 10, 'bold')
}

def create_auto_login_frame(parent):
    """创建美化的一键操作框架"""
    frame = ttk.LabelFrame(parent, text=f"{EMOJI['LOGIN']} 一键操作工具", padding="15")

    # 添加描述文字
    desc_label = ttk.Label(frame, text="快速执行登录和环境重置操作",
                          font=FONTS['small'], foreground=COLORS['text_secondary'])
    desc_label.pack(pady=(0, 15))

    # 按钮容器
    button_frame = ttk.Frame(frame)
    button_frame.pack(fill=tk.X)

    # 一键登录按钮（左边）
    login_button = ttk.Button(button_frame, text=f"{EMOJI['LOGIN']} 一键登录")
    login_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5), ipady=6)

    # 一键重置环境按钮（右边）
    reset_button = ttk.Button(button_frame, text=f"{EMOJI['RESET']} 一键重置环境")
    reset_button.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(5, 0), ipady=6)

    return frame, login_button, reset_button

def create_account_copy_frame(parent):
    """创建美化的账户信息框架"""
    frame = ttk.LabelFrame(parent, text=f"{EMOJI['USER']} 随机账户信息", padding="15")

    # 状态指示器
    status_frame = ttk.Frame(frame)
    status_frame.grid(row=0, column=0, columnspan=3, sticky="ew", pady=(0, 10))

    status_dot = ttk.Label(status_frame, text="●", foreground=COLORS['secondary'],
                          font=FONTS['body'])
    status_dot.pack(side=tk.LEFT)

    info_label = ttk.Label(status_frame, text="自动生成随机邮箱 (每秒更新)",
                          font=FONTS['small'], foreground=COLORS['text_secondary'])
    info_label.pack(side=tk.LEFT, padx=(5, 0))

    # 邮箱显示区域
    email_frame = ttk.Frame(frame)
    email_frame.grid(row=1, column=0, columnspan=3, sticky="ew", pady=(0, 5))
    email_frame.columnconfigure(1, weight=1)

    ttk.Label(email_frame, text="邮箱:", font=FONTS['body']).grid(row=0, column=0, sticky="w", padx=(0, 10))
    entry = ttk.Entry(email_frame, state='readonly', font=FONTS['mono'], width=40)
    entry.grid(row=0, column=1, sticky="ew", padx=(0, 10))

    button = ttk.Button(email_frame, text=f"{EMOJI['COPY']} 复制")
    button.grid(row=0, column=2)

    frame.columnconfigure(0, weight=1)
    return frame, entry, button

def create_email_credentials_frame(parent, prefix, password):
    """创建美化的邮箱凭据设置框架"""
    frame = ttk.LabelFrame(parent, text=f"{EMOJI['EMAIL']} 邮箱凭据设置", padding="15")

    # 添加说明
    desc_label = ttk.Label(frame, text="配置邮箱前缀和密码用于验证码接收",
                          font=FONTS['small'], foreground=COLORS['text_secondary'])
    desc_label.grid(row=0, column=0, columnspan=3, sticky="w", pady=(0, 15))

    # 邮箱前缀
    prefix_label = ttk.Label(frame, text="邮箱前缀:", font=FONTS['body'])
    prefix_label.grid(row=1, column=0, sticky="w", pady=(0, 10))

    prefix_entry = ttk.Entry(frame, font=FONTS['mono'], width=25)
    prefix_entry.insert(0, prefix)
    prefix_entry.grid(row=1, column=1, sticky="ew", padx=(10, 0), pady=(0, 10))

    # 显示完整邮箱格式
    format_label = ttk.Label(frame, text="@2925.com",
                           font=FONTS['small'], foreground=COLORS['text_secondary'])
    format_label.grid(row=1, column=2, sticky="w", padx=(5, 0), pady=(0, 10))

    # 邮箱密码
    password_label = ttk.Label(frame, text="邮箱密码:", font=FONTS['body'])
    password_label.grid(row=2, column=0, sticky="w", pady=(0, 15))

    password_entry = ttk.Entry(frame, font=FONTS['mono'], width=25)
    password_entry.insert(0, password)
    password_entry.grid(row=2, column=1, columnspan=2, sticky="ew", padx=(10, 0), pady=(0, 15))

    # 按钮区域
    button_frame = ttk.Frame(frame)
    button_frame.grid(row=3, column=0, columnspan=3, sticky="ew", pady=(10, 0))
    button_frame.columnconfigure(1, weight=1)

    # 邮箱监控器按钮（左边）
    email_client_button = ttk.Button(button_frame, text=f"{EMOJI['EMAIL']} 打开邮箱监控器")
    email_client_button.pack(side=tk.LEFT)

    # 保存按钮（右边）
    save_button = ttk.Button(button_frame, text=f"{EMOJI['SUCCESS']} 保存凭据")
    save_button.pack(side=tk.RIGHT)

    frame.columnconfigure(1, weight=1)
    return frame, prefix_entry, password_entry, save_button, email_client_button

def create_system_tools_frame(parent):
    """创建美化的系统工具框架"""
    # 由于邮箱监控器按钮已移到邮箱凭据框架，重置环境按钮已移到一键操作框架
    # 这个框架现在可以用于其他系统工具，或者返回None表示不需要
    return None

def create_browser_settings_frame(parent, browser_path):
    """创建美化的浏览器设置框架"""
    frame = ttk.LabelFrame(parent, text="🌐 浏览器设置", padding="15")

    # 添加说明
    desc_label = ttk.Label(frame, text="配置Chrome浏览器路径用于自动化操作",
                          font=FONTS['small'], foreground=COLORS['text_secondary'])
    desc_label.grid(row=0, column=0, columnspan=3, sticky="w", pady=(0, 15))

    # 路径标签
    path_label = ttk.Label(frame, text="Chrome路径:", font=FONTS['body'])
    path_label.grid(row=1, column=0, sticky="w", pady=(0, 10))

    # 路径输入框
    path_entry = ttk.Entry(frame, font=FONTS['mono'], width=35)
    path_entry.insert(0, browser_path)
    path_entry.grid(row=1, column=1, sticky="ew", padx=(10, 10), pady=(0, 10))

    # 浏览按钮
    browse_button = ttk.Button(frame, text="📁 浏览...")
    browse_button.grid(row=1, column=2, pady=(0, 10))

    # 路径状态指示
    if browser_path and len(browser_path) > 0:
        status_text = "✅ 已配置"
        status_color = COLORS['secondary']
    else:
        status_text = "⚠️ 未配置"
        status_color = COLORS['accent']

    status_label = ttk.Label(frame, text=status_text,
                           font=FONTS['small'], foreground=status_color)
    status_label.grid(row=2, column=1, sticky="w", padx=(10, 0), pady=(0, 10))

    # 保存按钮
    save_button = ttk.Button(frame, text=f"{EMOJI['SUCCESS']} 保存路径")
    save_button.grid(row=2, column=2, sticky="e", pady=(0, 0))

    frame.columnconfigure(1, weight=1)
    return frame, path_entry, browse_button, save_button