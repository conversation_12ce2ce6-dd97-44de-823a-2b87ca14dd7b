"""
This script packages the tkinter application into a single executable file using PyInstaller.

Usage:
1. Make sure you have PyInstaller installed:
   pip install pyinstaller

2. Make sure you have all the project dependencies installed (from requirements.txt):
   pip install -r requirements.txt

3. Place this script in the root directory of your project.

4. Run the script from your terminal:
   python build.py

The final executable will be placed in the root directory.
"""

import os
import shutil
import PyInstaller.__main__
import platform
import DrissionPage

# --- Configuration ---
# The main script of your application
SCRIPT_NAME = '主程序.py'
# The name for the final executable
EXE_NAME = '续杯工具'
# The directory containing webdrivers
DRIVERS_DIR = 'drivers'
# The directory for the final executable
DIST_DIR = './dist'
# Build directory
BUILD_DIR = './build'


def build():
    """
    Runs the PyInstaller build process.
    """
    print("--- 🚀 Starting build process ---")

    pyinstaller_options = [
        '--name', EXE_NAME,
        '--onefile',
        '--windowed',  # 无控制台窗口 (这个参数已经足够)
        '--clean',
        '--noconfirm',
        f'--distpath', DIST_DIR,
        f'--workpath', BUILD_DIR,
    ]

    # Add data files (like webdrivers)
    if os.path.isdir(DRIVERS_DIR):
        print(f"📦 Adding data directory: '{DRIVERS_DIR}'")
        pyinstaller_options.extend(['--add-data', f'{DRIVERS_DIR}{os.pathsep}{DRIVERS_DIR}'])
    else:
        print(f"⚠️ Warning: '{DRIVERS_DIR}' directory not found. The application may not function without it.")

    # Add DrissionPage config files
    try:
        drission_path = os.path.dirname(DrissionPage.__file__)
        drission_configs_path = os.path.join(drission_path, '_configs')
        if os.path.isdir(drission_configs_path):
            print(f"📦 Adding DrissionPage configs: '{drission_configs_path}'")
            pyinstaller_options.extend(['--add-data', f'{drission_configs_path}{os.pathsep}DrissionPage/_configs'])
        else:
            print("⚠️ Warning: DrissionPage configs directory not found.")
    except Exception as e:
        print(f"⚠️ Warning: Could not add DrissionPage configs. Error: {e}")

    # Add hidden imports that PyInstaller might miss
    hidden_imports = [
        'multiprocessing',
        'sqlite3',
        'pywinauto',
        'configparser',
        'DrissionPage',
        'colorama',
        'tkinter',
        'tkinter.ttk',
        'tkinter.scrolledtext',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'threading',
        'logging',
        'poplib',
        'email',
        'email.message',
        'bs4',
        'pyperclip'
    ]

    # Add Windows-specific imports
    if platform.system() == 'Windows':
        hidden_imports.append('winreg')
    for lib in hidden_imports:
        print(f"➕ Adding hidden import: '{lib}'")
        pyinstaller_options.extend(['--hidden-import', lib])

    command = [
        SCRIPT_NAME,
        *pyinstaller_options
    ]

    print("\n--- Running PyInstaller ---")
    print(f"Command: pyinstaller {' '.join(command)}")

    try:
        PyInstaller.__main__.run(command)
        print("\n--- ✅ PyInstaller build successful ---")

        exe_filename = f'{EXE_NAME}.exe' if platform.system() == 'Windows' else EXE_NAME
        src_exe_path = os.path.join(DIST_DIR, exe_filename)
        dest_exe_path = f'./{exe_filename}'

        if os.path.exists(src_exe_path):
            print(f"📦 Moving '{src_exe_path}' to project root -> '{dest_exe_path}'")
            if os.path.exists(dest_exe_path):
                os.remove(dest_exe_path)
            shutil.move(src_exe_path, './')
            print(f"✅ Executable is ready at: {os.path.abspath(dest_exe_path)}")
        else:
            print(f"❌ Error: Could not find the built executable at '{src_exe_path}'")

    except Exception as e:
        print(f"\n--- ❌ An error occurred during the build process ---")
        print(str(e))

    finally:
        print("\n--- 🧹 Cleaning up build artifacts ---")
        if os.path.isdir(BUILD_DIR):
            shutil.rmtree(BUILD_DIR)
        if os.path.isdir(DIST_DIR):
            shutil.rmtree(DIST_DIR)
        spec_file = f'{EXE_NAME}.spec'
        if os.path.exists(spec_file):
            os.remove(spec_file)
        print("--- ✨ Build process finished ---")


if __name__ == '__main__':
    if not os.path.exists(SCRIPT_NAME):
        print(f"❌ Error: Main script '{SCRIPT_NAME}' not found.")
        print("Please run this build script from the project's root directory.")
    else:
        build()
