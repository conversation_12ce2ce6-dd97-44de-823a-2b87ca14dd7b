import os
import platform
import subprocess
import webbrowser
import random
import string
import traceback
import time
import json

from DrissionPage import ChromiumOptions, ChromiumPage

from 配置管理 import get_config
from 工具模块 import EMOJI, get_default_browser_path
from 应用管理 import launch_email_client_process

# 使用绝对路径确保文件位置统一
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
MAX_WAIT_SECONDS = 90  # 等待90秒

def get_random_wait_time(config, timing_type='page_load_wait') -> float:
    """
    从配置中获取一个随机的等待时间，以模仿人类操作。
    """
    # 为简化演示，这里直接返回一个随机值。
    return random.uniform(0.5, 1.5)

def wait_for_verification_code(status_callback, code_container):
    """轮询一个共享列表以获取验证码。"""
    status_callback("正在等待接收验证码 (最长90秒)...")
    for i in range(MAX_WAIT_SECONDS):
        if code_container:
            code = code_container.pop(0)
            status_callback(f"COPY_AND_SHOW:{code}")
            return code
        
        # 每 15 秒更新一次状态
        if (i > 0 and (i + 1) % 15 == 0):
            status_callback(f"已等待 {i+1} 秒，接收中...")
            
        time.sleep(1)  # 每秒轮询一次
        
    status_callback("等待验证码超时。")
    return None

def run_auto_login_flow(monitoring_email, login_email, password, status_callback):
    """
    执行完整的自动登录流程。
    """
    chrome_window_to_close = None
    try:
        if not password:
            status_callback(f"错误: 邮箱密码未设置，请在凭据中设置并保存。")
            return

        if not monitoring_email or "@" not in monitoring_email:
            status_callback(f"错误: 用于监控的邮箱无效。")
            return

        status_callback("正在启动邮箱监控...")
        code_container = [] # 创建共享列表
        if not launch_email_client_process(monitoring_email, password, status_callback, show_status=False, code_container=code_container):
            status_callback(f"错误: 无法启动邮箱客户端，自动登录中止。")
            return
        status_callback(f"邮箱监控已在后台启动，监控账号: {monitoring_email}")

        status_callback("正在获取当前Chrome浏览器URL...")
        from 工具模块 import get_current_chrome_url
        target_url, chrome_window_to_close = get_current_chrome_url()
        if not target_url:
            status_callback(f"错误: 获取URL失败，请确保Chrome在前台打开了一个页面。")
            return

        status_callback("正在打开新的浏览器窗口...")
        co = ChromiumOptions()
        
        # 从配置中读取浏览器路径并设置
        config = get_config()
        if config:
            chrome_path = config.get('Browser', 'chrome_path', fallback=None)
            if chrome_path and os.path.exists(chrome_path):
                co.set_paths(browser_path=chrome_path)
                status_callback(f"使用配置文件中的Chrome路径: {chrome_path}")
            else:
                status_callback("配置文件中未找到有效Chrome路径，使用默认路径。")

        co.set_argument('--incognito')
        page = ChromiumPage(addr_or_opts=co)

        if chrome_window_to_close:
            status_callback("正在关闭原始Chrome窗口...")
            chrome_window_to_close.close()
            status_callback("原始窗口已关闭。")

        if not target_url.startswith('http'):
            target_url = 'https://' + target_url
        status_callback(f"成功获取URL: {target_url}")

        status_callback(f"使用随机邮箱进行登录: {login_email}")

        automate_login(page, target_url, login_email, status_callback, code_container)

    except Exception as e:
        status_callback(f"错误: 一键登录过程中发生未知错误: {e}")
        status_callback(traceback.format_exc())

def generate_random_email(status_callback=None):
    """Generates a random email based on the prefix in config.ini."""
    config = get_config()
    if not config:
        if status_callback:
            status_callback(f"{EMOJI['ERROR']} 无法加载配置，无法生成邮箱。")
        return None

    prefix = config.get('Email', 'prefix', fallback='').strip()
    if not prefix:
        if status_callback:
            status_callback(f"{EMOJI['ERROR']} 邮箱前缀未设置，无法生成邮箱。请在GUI中设置。")
        return None
    
    random_part = ''.join(random.choices(string.ascii_lowercase, k=7))
    email = f"{prefix}{random_part}@2925.com"
    return email

def automate_login(page, url, email, status_callback, code_container):
    """
    使用 DrissionPage 的核心自动化任务。
    假设浏览器/页面已打开。
    """
    try:
        status_callback(f"正在加载URL: {url}")
        page.get(url)

        # 步骤 1: 填写邮箱并点击 "Continue"
        status_callback("步骤 1: 正在填写邮箱并点击 'Continue'...")
        email_input = page.ele('@@name=email', timeout=10)
        email_input.input(email)
        continue_button = page.ele('@@type=submit', timeout=10)
        continue_button.click()
        status_callback("已点击 'Continue'，等待跳转...")

        # 步骤 2: 在下一页点击 "Email sign-in code"
        status_callback("步骤 2: 等待密码页面加载，准备点击验证码登录...")
        sign_in_code_button = page.ele('text:Email sign-in code', timeout=15)
        sign_in_code_button.click()
        status_callback("已点击 'Email sign-in code' 按钮。")

        # 步骤 3: 等待验证码
        status_callback("步骤 3: 启动验证码监听程序...")
        code = wait_for_verification_code(status_callback, code_container)

        if not code:
            final_message = "\n错误: 等待验证码超时。请手动输入验证码完成登录。\n\n注意：操作完成后，请您手动关闭此浏览器窗口。"
            status_callback(final_message)
            return

        # 步骤 4: 自动填写验证码
        try:
            status_callback(f"步骤 4: 成功获取验证码，正在自动填写...")
            for i, digit in enumerate(code):
                input_box = page.ele(f"@data-index={i}")
                input_box.input(digit)
                time.sleep(random.uniform(0.1, 0.3))
            
            status_callback("✅ 验证码填写完成。")
            final_message = "\n✅ 成功: 验证码已自动填写。\n\n如果页面没有自动跳转，请手动完成最后步骤。完成后请手动关闭此浏览器窗口。"
            status_callback(final_message)
        
        except Exception as e:
            status_callback(f"❌ 错误: 自动填写验证码时出错: {e}")
            final_message = "自动化流程中止，请手动输入验证码。"
            status_callback(final_message)

        return

    except Exception as e:
        error_msg = f"错误: 自动化过程中发生错误: {e}"
        status_callback(error_msg)
        status_callback(traceback.format_exc()) 